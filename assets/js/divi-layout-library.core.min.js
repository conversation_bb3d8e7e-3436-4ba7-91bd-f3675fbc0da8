(()=>{"use strict";const e=window.React,t=window.ReactDOM,a=class{constructor(){this.ajaxUrl=window.dllAjax?.ajaxUrl||"/wp-admin/admin-ajax.php",this.nonce=window.dllAjax?.nonce||"",this.strings=window.dllAjax?.strings||{}}async makeRequest(e,t={},a={}){const l={action:e,nonce:this.nonce,...t},o={method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams(l),...a};try{const e=await fetch(this.ajaxUrl,o);if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const t=await e.json();if(!t.success)throw new Error(t.data?.message||this.strings.error||"Request failed");return t.data}catch(e){throw console.error("API Request failed:",e),e}}async formatBuilderLayoutFile(e){const t=new FileReader;return new Promise((a,l)=>{t.onloadend=t=>{let l="";try{l=JSON.parse(t.target.result)}catch(t){const l=new File([JSON.stringify({})],e.name,{type:"application/json"});return a(l)}if("et_builder"===l.context){const t=e.name.replace(".json",""),o=Object.keys(l.data)[0],s=l.data[o],r={...l,context:"et_builder_layouts",data:{[o]:{ID:parseInt(o,10),post_title:t,post_name:t,post_content:s,post_excerpt:"",post_status:"publish",comment_status:"closed",ping_status:"closed",post_type:"et_pb_layout",post_meta:{_et_pb_built_for_post_type:["page"]},terms:{1:{name:"layout",slug:"layout",taxonomy:"layout_type"}}}}},n=new File([JSON.stringify(r)],e.name,{type:"application/json"});a(n)}else a(e)},t.onerror=()=>{t.abort(),l()},t.readAsText(e)})}async importLayout(e,t={}){try{if(console.log("window.etCore:",window.etCore),console.log("window.etCorePortability:",window.etCorePortability),!window.etCore||!window.etCore.portability)return console.warn("Divi portability system not available, falling back to direct AJAX"),this.importLayoutFallback(e,t);const a=await this.formatBuilderLayoutFile(e),l=await this.readFileAsText(a);let o="et_builder_layouts";try{const e=JSON.parse(l);o=e.context||"et_builder_layouts"}catch(e){}return console.log("Using Divi portability system with context:",o),console.log("Available etCore.portability methods:",Object.keys(window.etCore.portability)),new Promise((e,l)=>{const s={action:"et_core_portability_import",context:o,file:a,content:!1,timestamp:0,post:t.createPage?0:jQuery("#post_ID").val()||0,replace:(t.createPage,"0"),include_global_presets:t.includeGlobalPresets?"1":"0",page:1,nonce:window.etCorePortability?.nonces?.import||window.dllAjax.portability_nonce};console.log("Import data:",s),window.etCore.portability.ajaxAction(s,function(a){console.log("Divi portability response:",a),t.createPage&&a&&a.data?this.createPageWithLayout(a.data,t.pageTitle).then(e).catch(l):e({success:!0,data:a.data||a,message:"Layout imported successfully"})}.bind(this),!0)})}catch(e){return console.error("Import error:",e),{success:!1,message:e.message||"Import preparation failed"}}}async importLayoutFallback(e,t={}){try{const a=await this.formatBuilderLayoutFile(e),l=await this.readFileAsText(a);let o="et_builder_layouts";try{const e=JSON.parse(l);o=e.context||"et_builder_layouts"}catch(e){}return console.log("Using fallback jQuery AJAX with context:",o),new Promise((e,l)=>{const s={action:"et_core_portability_import",context:o,nonce:window.dllAjax.portability_nonce,file:a,content:!1,timestamp:0,post:t.createPage?0:jQuery("#post_ID").val()||0,replace:(t.createPage,"0"),include_global_presets:t.includeGlobalPresets?"1":"0",page:1},r=new FormData;Object.keys(s).forEach(function(e){const t=s[e];"file"===e?r.append("file",t,t.name):r.append(e,t)}),jQuery.ajax({type:"POST",url:this.ajaxUrl,data:r,processData:!1,contentType:!1,success:t=>{console.log("Fallback import response:",t),!t||void 0===t.data&&!1===t.success?l(new Error("Import failed - no data returned")):e({success:!0,data:t.data||t,message:"Layout imported successfully (fallback)"})},error:(e,t,a)=>{console.error("Fallback AJAX error:",e,t,a),l(new Error(`Network error: ${a}`))}})})}catch(e){return console.error("Fallback import error:",e),{success:!1,message:e.message||"Fallback import preparation failed"}}}async createPageWithLayout(e,t,a="draft"){return this.makeRequest("dll_create_page_with_layout",{layout_data:JSON.stringify(e),page_title:t,page_status:a})}async exportLayout(e,t=""){return this.makeRequest("dll_export_layout",{layout_id:e,export_name:t})}async getAvailableLayouts(){return this.makeRequest("dll_get_layouts")}async verifyImportSuccess(e){try{if(e&&e.data&&e.data.timestamp){console.log("Import timestamp:",e.data.timestamp);const t=await this.getAvailableLayouts();return console.log("Available layouts after import:",t),{success:!0,message:"Import verification completed",layoutCount:t.length||0}}return{success:!1,message:"No import data to verify"}}catch(e){return console.error("Import verification failed:",e),{success:!1,message:"Verification failed: "+e.message}}}downloadLayoutFile(e,t){try{const a=JSON.stringify(e,null,2),l=new Blob([a],{type:"application/json"}),o=URL.createObjectURL(l),s=document.createElement("a");s.href=o,s.download=`${t}.json`,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(o)}catch(e){throw console.error("Download failed:",e),new Error("Failed to download layout file")}}async loadLayoutFromFile(e){try{const t=await fetch(e);if(!t.ok)throw new Error(`Failed to load layout file: ${t.status}`);const a=await t.json();if(!this.validateLayoutData(a))throw new Error("Invalid layout data structure");return a}catch(e){throw console.error("Failed to load layout from file:",e),e}}validateLayoutData(e){if(!e||"object"!=typeof e)return!1;const t=["context","data"];for(const a of t)if(!e.hasOwnProperty(a))return!1;return!!["et_builder","et_builder_layouts"].includes(e.context)&&!(!e.data||"object"!=typeof e.data)}readFileAsText(e){return new Promise((t,a)=>{const l=new FileReader;l.onload=e=>t(e.target.result),l.onerror=()=>a(l.error),l.readAsText(e)})}async handleFileUpload(e){return new Promise((t,a)=>{if(!e)return void a(new Error("No file provided"));if("application/json"!==e.type&&!e.name.endsWith(".json"))return void a(new Error(this.strings.invalidFile||"Invalid file format. Please select a JSON file."));const l=new FileReader;l.onload=l=>{try{JSON.parse(l.target.result),t(e)}catch(e){a(new Error("Failed to parse JSON file"))}},l.onerror=()=>{a(new Error("Failed to read file"))},l.readAsText(e)})}getString(e){return this.strings[e]||e}},l=({layout:t,viewMode:a,onImport:l,onPreview:o})=>{const[s,r]=(0,e.useState)(!1),[n,i]=(0,e.useState)(!1),[c,d]=(0,e.useState)(!1),m=()=>{i(!0)},u=()=>{d(!0),i(!0)},p=e=>{e.preventDefault(),e.stopPropagation(),l()},y=e=>{e.preventDefault(),e.stopPropagation(),o()},g=()=>(0,e.createElement)("div",{className:"dll-layout-card__actions"},(0,e.createElement)("button",{className:"dll-button dll-button--primary dll-button--small",onClick:p,title:"Import this layout"},(0,e.createElement)("span",{className:"dashicons dashicons-download"}),"Import"),(0,e.createElement)("button",{className:"dll-button dll-button--secondary dll-button--small",onClick:y,title:"Preview this layout"},(0,e.createElement)("span",{className:"dashicons dashicons-visibility"}),"Preview")),h=()=>(0,e.createElement)("div",{className:"dll-layout-card__image-container"},!n&&!c&&(0,e.createElement)("div",{className:"dll-layout-card__image-placeholder"},(0,e.createElement)("div",{className:"dll-loading__spinner dll-loading__spinner--small"})),c?(0,e.createElement)("div",{className:"dll-layout-card__image-error"},(0,e.createElement)("span",{className:"dashicons dashicons-format-image"}),(0,e.createElement)("span",null,"Image not available")):(0,e.createElement)("img",{src:t.previewImage,alt:t.name,className:"dll-layout-card__image "+(s?"dll-layout-card__image--scrolling":""),onLoad:m,onError:u,style:{display:n?"block":"none"}}),s&&(0,e.createElement)("div",{className:"dll-layout-card__overlay"},g())),_=()=>(0,e.createElement)("div",{className:"dll-layout-card__content"},(0,e.createElement)("h3",{className:"dll-layout-card__title"},t.name),(0,e.createElement)("p",{className:"dll-layout-card__category"},t.category),(0,e.createElement)("p",{className:"dll-layout-card__description"},t.description),"list"===a&&(0,e.createElement)("div",{className:"dll-layout-card__actions-list"},g()));return"grid"===a?(0,e.createElement)("div",{className:"dll-layout-card dll-layout-card--grid",onMouseEnter:()=>r(!0),onMouseLeave:()=>r(!1)},h(),_()):(0,e.createElement)("div",{className:"dll-layout-card dll-layout-card--list",onMouseEnter:()=>r(!0),onMouseLeave:()=>r(!1)},(0,e.createElement)("div",{className:"dll-layout-card__image-wrapper"},h()),(0,e.createElement)("div",{className:"dll-layout-card__content-wrapper"},_()))},o=({categories:t,selectedCategory:a,onCategoryChange:l})=>(0,e.createElement)("div",{className:"dll-sidebar"},(0,e.createElement)("div",{className:"dll-sidebar__header"},(0,e.createElement)("h3",{className:"dll-sidebar__title"},(0,e.createElement)("span",{className:"dashicons dashicons-category"}),"Categories")),(0,e.createElement)("div",{className:"dll-sidebar__content"},(0,e.createElement)("ul",{className:"dll-category-list"},t.map(t=>(0,e.createElement)("li",{key:t,className:"dll-category-list__item"},(0,e.createElement)("button",{className:"dll-category-list__button "+(a===t?"dll-category-list__button--active":""),onClick:()=>(e=>{l(e)})(t)},(0,e.createElement)("span",{className:"dll-category-list__name"},(e=>"all"===e?"All Categories":e)(t)),"")))))),s=({layout:t,onClose:l})=>{const[o,s]=(0,e.useState)("library"),[r,n]=(0,e.useState)(""),[i,c]=(0,e.useState)("draft"),[d,m]=(0,e.useState)(!1),[u,p]=(0,e.useState)(0),[y,g]=(0,e.useState)(null),[h,_]=(0,e.useState)(null),[E,v]=(0,e.useState)(!1),w=new a,b=e=>{s(e),_(null),"page"!==e||r||n(t?.name||"")},f=()=>{d||l()};return(0,e.createElement)("div",{className:"dll-modal-overlay",onClick:f},(0,e.createElement)("div",{className:"dll-modal dll-import-modal",onClick:e=>e.stopPropagation()},(0,e.createElement)("div",{className:"dll-modal__header"},(0,e.createElement)("h2",{className:"dll-modal__title"},"Import Layout: ",t?.name),(0,e.createElement)("button",{className:"dll-modal__close",onClick:f,disabled:d},(0,e.createElement)("span",{className:"dashicons dashicons-no-alt"}))),(0,e.createElement)("div",{className:"dll-modal__content"},h?(0,e.createElement)("div",{className:"dll-import-error"},(0,e.createElement)("div",{className:"dll-import-error__icon"},(0,e.createElement)("span",null,"😞")),(0,e.createElement)("h3",{className:"dll-import-error__title"},"Import Failed"),(0,e.createElement)("p",{className:"dll-import-error__message"},h),(0,e.createElement)("button",{className:"dll-button dll-button--primary",onClick:()=>{_(null),p(0)}},"Try Again")):y?(0,e.createElement)("div",{className:"dll-import-success"},E&&(0,e.createElement)("div",{className:"dll-confetti"},Array.from({length:50}).map((t,a)=>(0,e.createElement)("div",{key:a,className:"dll-confetti__piece",style:{left:100*Math.random()+"%",animationDelay:3*Math.random()+"s",backgroundColor:["#ff6b6b","#4ecdc4","#45b7d1","#96ceb4","#feca57"][Math.floor(5*Math.random())]}}))),(0,e.createElement)("div",{className:"dll-import-success__content"},(0,e.createElement)("div",{className:"dll-import-success__icon"},(0,e.createElement)("span",{className:"dashicons dashicons-yes-alt"})),(0,e.createElement)("h3",{className:"dll-import-success__title"},"Congratulations! 🎉"),(0,e.createElement)("p",{className:"dll-import-success__message"},"page"===o?`Page "${r}" has been created successfully!`:"Layout has been imported to your library successfully!"),y?.data?.edit_url&&(0,e.createElement)("div",{className:"dll-import-success__actions"},(0,e.createElement)("a",{href:y.data.edit_url,className:"dll-button dll-button--primary",target:"_blank",rel:"noopener noreferrer"},"Edit Page"),y.data.view_url&&(0,e.createElement)("a",{href:y.data.view_url,className:"dll-button dll-button--secondary",target:"_blank",rel:"noopener noreferrer"},"View Page")))):(0,e.createElement)(e.Fragment,null,d?(0,e.createElement)("div",{className:"dll-import-progress"},(0,e.createElement)("p",{className:"dll-import-progress__text"},w.getString("importing")),(0,e.createElement)("div",{className:"dll-progress"},(0,e.createElement)("div",{className:"dll-progress__bar"},(0,e.createElement)("div",{className:"dll-progress__fill",style:{width:`${u}%`}})),(0,e.createElement)("div",{className:"dll-progress__text"},u<100?`${Math.round(u)}%`:"Complete!"))):(0,e.createElement)("div",{className:"dll-import-form"},(0,e.createElement)("div",{className:"dll-import-options"},(0,e.createElement)("h3",null,"Import Options"),(0,e.createElement)("label",{className:"dll-radio-option"},(0,e.createElement)("input",{type:"radio",name:"importType",value:"page",checked:"page"===o,onChange:()=>b("page")}),(0,e.createElement)("span",{className:"dll-radio-option__label"},(0,e.createElement)("strong",null,"Make a New Page")," with this layout")),(0,e.createElement)("label",{className:"dll-radio-option"},(0,e.createElement)("input",{type:"radio",name:"importType",value:"library",checked:"library"===o,onChange:()=>b("library")}),(0,e.createElement)("span",{className:"dll-radio-option__label"},(0,e.createElement)("strong",null,"Just Import Layout")," (add to Divi library)"))),"page"===o&&(0,e.createElement)("div",{className:"dll-page-options"},(0,e.createElement)("div",{className:"dll-form-group"},(0,e.createElement)("label",{htmlFor:"pageName",className:"dll-form-label"},"Page Name *"),(0,e.createElement)("input",{type:"text",id:"pageName",className:"dll-form-input",value:r,onChange:e=>n(e.target.value),placeholder:"Enter page name",required:!0})),(0,e.createElement)("div",{className:"dll-form-group"},(0,e.createElement)("label",{htmlFor:"pageStatus",className:"dll-form-label"},"Page Status"),(0,e.createElement)("select",{id:"pageStatus",className:"dll-form-select",value:i,onChange:e=>c(e.target.value)},(0,e.createElement)("option",{value:"draft"},"Draft"),(0,e.createElement)("option",{value:"publish"},"Published"),(0,e.createElement)("option",{value:"private"},"Private"))))))),!d&&!y&&!h&&(0,e.createElement)("div",{className:"dll-modal__footer"},(0,e.createElement)("button",{className:"dll-button dll-button--secondary",onClick:f},"Cancel"),(0,e.createElement)("button",{className:"dll-button dll-button--primary",onClick:async()=>{if("page"===o&&!r.trim()&&(_(w.getString("pageNameRequired")||"Page name is required"),1))return;m(!0),_(null),g(null);const e=(()=>{p(0);const e=setInterval(()=>{p(t=>t>=90?(clearInterval(e),90):t+20*Math.random())},200);return e})();try{const a=await fetch(t.jsonFile);if(!a.ok)throw new Error("Failed to load layout file");const l=await a.text(),s=t.jsonFile.split("/").pop()||"layout.json",n=new File([l],s,{type:"application/json"}),i={includeGlobalPresets:!1,createPage:"page"===o,pageTitle:"page"===o?r.trim():void 0};console.info(i);const c=await w.importLayout(n,i);if(clearInterval(e),p(100),c.success){console.log("Import completed, verifying...");const e=await w.verifyImportSuccess(c);console.log("Verification result:",e),c.verification=e}g(c),v(!0),setTimeout(()=>{v(!1)},3e3)}catch(t){clearInterval(e),_(t.message||w.getString("error")),p(0)}finally{m(!1)}}},(0,e.createElement)("span",{className:"dashicons dashicons-download"}),"Import Layout"))))},r=({onClose:t})=>{const[l,o]=(0,e.useState)([]),[s,r]=(0,e.useState)(null),[n,i]=(0,e.useState)(""),[c,d]=(0,e.useState)(!0),[m,u]=(0,e.useState)(!1),[p,y]=(0,e.useState)(null),[g,h]=(0,e.useState)(!1),_=new a;(0,e.useEffect)(()=>{E()},[]);const E=async()=>{try{d(!0),y(null);const e=await _.getAvailableLayouts();o(e.layouts||[])}catch(e){y(e.message||"Failed to load layouts")}finally{d(!1)}},v=()=>{m||t()};return(0,e.createElement)("div",{className:"dll-modal-overlay",onClick:v},(0,e.createElement)("div",{className:"dll-modal dll-export-modal",onClick:e=>e.stopPropagation()},(0,e.createElement)("div",{className:"dll-modal__header"},(0,e.createElement)("h2",{className:"dll-modal__title"},(0,e.createElement)("span",{className:"dashicons dashicons-download"}),"Export Layout"),(0,e.createElement)("button",{className:"dll-modal__close",onClick:v,disabled:m},(0,e.createElement)("span",{className:"dashicons dashicons-no-alt"}))),(0,e.createElement)("div",{className:"dll-modal__content"},c?(0,e.createElement)("div",{className:"dll-export-loading"},(0,e.createElement)("div",{className:"dll-loading__spinner"}),(0,e.createElement)("p",null,"Loading available layouts...")):p&&!s?(0,e.createElement)("div",{className:"dll-export-error"},(0,e.createElement)("div",{className:"dll-export-error__icon"},(0,e.createElement)("span",{className:"dashicons dashicons-warning"})),(0,e.createElement)("h3",null,"Error"),(0,e.createElement)("p",null,p),(0,e.createElement)("button",{className:"dll-button dll-button--primary",onClick:E},"Try Again")):g?(0,e.createElement)("div",{className:"dll-export-success"},(0,e.createElement)("div",{className:"dll-export-success__icon"},(0,e.createElement)("span",{className:"dashicons dashicons-yes-alt"})),(0,e.createElement)("h3",null,"Export Successful!"),(0,e.createElement)("p",null,"Your layout has been downloaded successfully.")):(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{className:"dll-export-step"},(0,e.createElement)("h3",null,"1. Select Layout to Export"),(0,e.createElement)("div",{className:"dll-layout-list"},0===l.length?(0,e.createElement)("div",{className:"dll-layout-list__empty"},(0,e.createElement)("p",null,"No layouts available for export."),(0,e.createElement)("p",null,"Create some pages with Divi Builder first.")):(0,e.createElement)("div",{className:"dll-layout-list__items"},l.map(t=>{return(0,e.createElement)("div",{key:t.id,className:"dll-layout-item "+(s?.id===t.id?"dll-layout-item--selected":""),onClick:()=>(e=>{r(e),i(e.title||""),y(null)})(t)},(0,e.createElement)("div",{className:"dll-layout-item__content"},(0,e.createElement)("h4",{className:"dll-layout-item__title"},t.title||"Untitled"),(0,e.createElement)("div",{className:"dll-layout-item__meta"},(0,e.createElement)("span",{className:"dll-layout-item__type"},t.type),(0,e.createElement)("span",{className:"dll-layout-item__status"},t.status),(0,e.createElement)("span",{className:"dll-layout-item__date"},"Modified: ",(a=t.modified,new Date(a).toLocaleDateString())))),(0,e.createElement)("div",{className:"dll-layout-item__actions"},(0,e.createElement)("a",{href:t.edit_url,className:"dll-layout-item__edit",target:"_blank",rel:"noopener noreferrer",onClick:e=>e.stopPropagation(),title:"Edit this layout"},(0,e.createElement)("span",{className:"dashicons dashicons-edit"}))));var a})))),s&&(0,e.createElement)("div",{className:"dll-export-step"},(0,e.createElement)("h3",null,"2. Export Options"),(0,e.createElement)("div",{className:"dll-export-form"},(0,e.createElement)("div",{className:"dll-form-group"},(0,e.createElement)("label",{htmlFor:"exportName",className:"dll-form-label"},"Export Name (optional)"),(0,e.createElement)("input",{type:"text",id:"exportName",className:"dll-form-input",value:n,onChange:e=>i(e.target.value),placeholder:"Enter custom name for export"}),(0,e.createElement)("p",{className:"dll-form-help"},"Leave empty to use the layout title as filename.")))),p&&(0,e.createElement)("div",{className:"dll-export-error-inline"},(0,e.createElement)("span",{className:"dashicons dashicons-warning"}),p))),!c&&!g&&l.length>0&&(0,e.createElement)("div",{className:"dll-modal__footer"},(0,e.createElement)("button",{className:"dll-button dll-button--secondary",onClick:v,disabled:m},"Cancel"),(0,e.createElement)("button",{className:"dll-button dll-button--primary",onClick:async()=>{if(s){u(!0),y(null);try{const e=await _.exportLayout(s.id,n.trim()||s.title),a=n.trim()||s.title||"divi_layout";_.downloadLayoutFile(e.export_data,a),h(!0),setTimeout(()=>{t()},2e3)}catch(e){y(e.message||"Export failed")}finally{u(!1)}}else y(_.getString("selectLayout")||"Please select a layout to export")},disabled:!s||m},m?(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{className:"dll-loading__spinner dll-loading__spinner--small"}),_.getString("exporting")):(0,e.createElement)(e.Fragment,null,(0,e.createElement)("span",{className:"dashicons dashicons-download"}),"Export & Download")))))},{plugin_root_url:n}=window.dllAjax,i=()=>{const[t,n]=(0,e.useState)([]),[i,c]=(0,e.useState)([]),[d,m]=(0,e.useState)("all"),[u,p]=(0,e.useState)("grid"),[y,g]=(0,e.useState)(!0),[h,_]=(0,e.useState)(null),[E,v]=(0,e.useState)(!1),[w,b]=(0,e.useState)(!1),[f,N]=(0,e.useState)(null);new a,(0,e.useEffect)(()=>{x()},[]),(0,e.useEffect)(()=>{k()},[t,d]);const x=()=>{try{g(!0),n([{id:"layout-1",name:"Modern Business",category:"Business",description:"A clean and modern business layout for corporate sites.",previewImage:"/wp-content/plugins/divi-layout-library/assets/images/previews/business-modern.jpg",previewLink:"https://demo.example.com/business-modern",jsonFile:"https://raw.githubusercontent.com/hrrarya/layouts/refs/heads/main/Web-Services.json"},{id:"layout-2",name:"Creative Portfolio",category:"Portfolio",description:"A creative portfolio layout perfect for showcasing work.",previewImage:"/wp-content/plugins/divi-layout-library/assets/images/previews/portfolio-creative.jpg",previewLink:"https://demo.example.com/portfolio-creative",jsonFile:"https://raw.githubusercontent.com/hrrarya/layouts/refs/heads/main/portfolio-creative.json"},{id:"layout-3",name:"Restaurant Menu",category:"Restaurant",description:"An elegant restaurant layout with menu showcase.",previewImage:"/wp-content/plugins/divi-layout-library/assets/images/previews/restaurant-menu.jpg",previewLink:"https://demo.example.com/restaurant-menu",jsonFile:"https://raw.githubusercontent.com/hrrarya/layouts/refs/heads/main/restaurant-menu.json"},{id:"layout-4",name:"Tech Startup",category:"Business",description:"A modern tech startup layout with bold design.",previewImage:"/wp-content/plugins/divi-layout-library/assets/images/previews/tech-startup.jpg",previewLink:"https://demo.example.com/tech-startup",jsonFile:"https://raw.githubusercontent.com/hrrarya/layouts/refs/heads/main/blurbcore.json"},{id:"layout-5",name:"Photography Studio",category:"Portfolio",description:"A stunning photography portfolio layout.",previewImage:"/wp-content/plugins/divi-layout-library/assets/images/previews/photography-studio.jpg",previewLink:"https://demo.example.com/photography-studio",jsonFile:"/wp-content/plugins/divi-layout-library/assets/layouts/photography-studio.json"},{id:"layout-6",name:"Coffee Shop",category:"Restaurant",description:"A cozy coffee shop layout with warm colors.",previewImage:"/wp-content/plugins/divi-layout-library/assets/images/previews/coffee-shop.jpg",previewLink:"https://demo.example.com/coffee-shop",jsonFile:"/wp-content/plugins/divi-layout-library/assets/layouts/coffee-shop.json"},{id:"layout-7",name:"Gardener Shop",category:"Garden",description:"A garden shop.",previewImage:"/wp-content/plugins/divi-layout-library/assets/images/previews/coffee-shop.jpg",previewLink:"https://demo.example.com/coffee-shop",jsonFile:"https://raw.githubusercontent.com/hrrarya/layouts/refs/heads/main/Gardener-All-Layouts-Import.json"}]),g(!1)}catch(e){_("Failed to load layouts"),g(!1)}},k=()=>{c("all"===d?t:t.filter(e=>e.category===d))};return y?(0,e.createElement)("div",{className:"dll-dashboard dll-dashboard--loading"},(0,e.createElement)("div",{className:"dll-loading"},(0,e.createElement)("div",{className:"dll-loading__spinner"}),(0,e.createElement)("p",null,"Loading layouts..."))):h?(0,e.createElement)("div",{className:"dll-dashboard dll-dashboard--error"},(0,e.createElement)("div",{className:"dll-error"},(0,e.createElement)("h3",null,"Error"),(0,e.createElement)("p",null,h),(0,e.createElement)("button",{onClick:x,className:"dll-button dll-button--primary"},"Try Again"))):(0,e.createElement)("div",{className:"dll-dashboard"},(0,e.createElement)("div",{className:"dll-dashboard__header"},(0,e.createElement)("h1",{className:"dll-dashboard__title"},"Divi Layout Library"),(0,e.createElement)("div",{className:"dll-dashboard__toolbar"},(0,e.createElement)("div",{className:"dll-view-toggle"},(0,e.createElement)("button",{className:"dll-view-toggle__button "+("grid"===u?"dll-view-toggle__button--active":""),onClick:()=>p("grid"),title:"Grid View"},(0,e.createElement)("span",{className:"dashicons dashicons-grid-view"})),(0,e.createElement)("button",{className:"dll-view-toggle__button "+("list"===u?"dll-view-toggle__button--active":""),onClick:()=>p("list"),title:"List View"},(0,e.createElement)("span",{className:"dashicons dashicons-list-view"}))),(0,e.createElement)("button",{className:"dll-button dll-button--secondary",onClick:()=>{b(!0)}},(0,e.createElement)("span",{className:"dashicons dashicons-download"}),"Export Layout"))),(0,e.createElement)("div",{className:"dll-dashboard__content"},(0,e.createElement)(o,{categories:(()=>{const e=["all"];return t.forEach(t=>{e.includes(t.category)||e.push(t.category)}),e})(),selectedCategory:d,onCategoryChange:m}),(0,e.createElement)("div",{className:"dll-dashboard__main"},(0,e.createElement)("div",{className:`dll-layouts dll-layouts--${u}`},0===i.length?(0,e.createElement)("div",{className:"dll-layouts__empty"},(0,e.createElement)("p",null,"No layouts found for the selected category.")):i.map(t=>(0,e.createElement)(l,{key:t.id,layout:t,viewMode:u,onImport:()=>(e=>{N(e),v(!0)})(t),onPreview:()=>(e=>{e.previewLink&&window.open(e.previewLink,"_blank")})(t)}))))),E&&(0,e.createElement)(s,{layout:f,onClose:()=>{v(!1),N(null)}}),w&&(0,e.createElement)(r,{onClose:()=>b(!1)}))},c=()=>(0,e.createElement)("div",{className:"dll-app"},(0,e.createElement)(i,null)),d=window.wp.hooks;window.divi_layout_library_hooks=(0,d.createHooks)(),document.addEventListener("DOMContentLoaded",function(){const a=document.getElementById("divi-layout-library-body");(0,t.createRoot)(a).render((0,e.createElement)(c,null))})})();